import type { Account } from "~/api/types.gen";

import AccountCard from "./account-card";

interface Props {
  accounts: Account[];
  onEditAccount: (account: Account) => void;
  onDeleteAccount: (account: Account) => void;
}

export default function AccountsListFlat(props: Props) {
  const { accounts, onEditAccount, onDeleteAccount } = props;

  return (
    <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
      {accounts.map((account) => (
        <AccountCard key={account.id} account={account} onEdit={onEditAccount} onDelete={onDeleteAccount} />
      ))}
    </div>
  );
}
