import type { Account } from "../types";

import { Link } from "@tanstack/react-router";
import { Edit, MoreHorizontal, Trash2 } from "lucide-react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";
import { cn } from "~/lib/utils";

import { getAccountTypeIcon, getAccountTypeLabel } from "../utils";

interface Props {
  account: Account;
  onEdit: (account: Account) => void;
  onDelete: (account: Account) => void;
  className?: string;
}

export default function AccountCard({ account, onEdit, onDelete, className }: Props) {
  const baseCurrency = useBaseCurrency();

  const Icon = getAccountTypeIcon(account.type);
  const typeLabel = getAccountTypeLabel(account.type);

  return (
    <div
      className={cn("bg-card hover:bg-accent/50 rounded-lg border p-4 transition-colors", className)}
      style={{ borderLeftColor: account.color || undefined, borderLeftWidth: account.color ? 4 : 1 }}
    >
      <div className="flex items-start justify-between">
        <div className="flex flex-1 items-start gap-3">
          <div className="flex-shrink-0">
            <Icon className="text-muted-foreground h-5 w-5" />
          </div>

          <div className="min-w-0 flex-1">
            <Link to="/accounts/$accountId" params={{ accountId: account.id }} className="block">
              <h3 className="text-foreground hover:text-primary font-semibold transition-colors">{account.name}</h3>
            </Link>

            <p className="text-muted-foreground mt-1 text-sm">
              {typeLabel} • {account.currency}
            </p>

            {account.description && (
              <p className="text-muted-foreground mt-1 line-clamp-2 text-sm">{account.description}</p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="text-right">
            <div className="text-foreground font-semibold">
              {formatCurrency(account.currency, account.currentBalance)}
            </div>
            {account.currency !== baseCurrency && (
              <div className="text-muted-foreground text-sm">
                {formatCurrency(baseCurrency, account.baseCurrentBalance)}
              </div>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="place-self-start">
                <MoreHorizontal className="size-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(account)}>
                <Edit className="mr-1" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDelete(account)} variant="destructive">
                <Trash2 className="mr-1" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {!account.isActive && (
        <div className="bg-muted text-muted-foreground mt-3 inline-flex items-center rounded-full px-2 py-1 text-xs">
          Archived
        </div>
      )}
    </div>
  );
}
