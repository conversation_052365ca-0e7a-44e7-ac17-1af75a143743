import type { AccountCreateRequest } from "~/api/types.gen";
import type { AccountGroup } from "../types";

import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import z from "zod";

import { zAccountCreateRequest } from "~/api/zod.gen";
import { InputColor, InputCurrency, InputSelect, InputText, InputTextarea } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";
import { useBaseCurrency } from "~/features/auth/hooks";

import { accountTypeOptions } from "../constants";

interface Props {
  accountGroups: AccountGroup[];
  preselectedGroupId?: string | null;
  onSubmit: (data: AccountCreateRequest) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export default function AccountFormCreate(props: Props) {
  const { accountGroups, preselectedGroupId, onSubmit, onCancel, isLoading = false } = props;

  const baseCurrency = useBaseCurrency();

  const defaultValues = useMemo<AccountCreateRequest>(
    () => ({
      name: "",
      type: "cash",
      currency: baseCurrency,
      groupId: preselectedGroupId ?? "",
      description: "",
      color: "",
      openingBalance: "0.00",
    }),
    [baseCurrency, preselectedGroupId]
  );

  const form = useForm<AccountCreateRequest>({
    resolver: zodResolver(
      zAccountCreateRequest.merge(
        z.object({
          groupId: z.string().uuid().nullable(),
        })
      )
    ),
    defaultValues,
  });

  const resetForm = form.reset;

  // Reset form when preselectedGroupId or baseCurrency changes
  useEffect(() => {
    resetForm(defaultValues);
  }, [defaultValues, resetForm]);

  const handleSubmit = form.handleSubmit((data: AccountCreateRequest) => {
    // Convert empty strings to null/undefined
    const processedData = {
      ...data,
      description: data.description?.trim() || null,
      color: data.color?.trim() || undefined,
      groupId: data.groupId || null,
    };

    onSubmit(processedData);
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="name"
            label="Account Name"
            placeholder="e.g., Main Checking Account"
          />

          <InputSelect
            values={accountGroups.map((group) => ({ value: group.id, label: group.name }))}
            control={form.control}
            name="groupId"
            label="Account group"
            hint="Optional"
            placeholder="No group"
            clearable
          />

          <InputCurrency control={form.control} name="currency" label="Currency" />

          <InputSelect values={accountTypeOptions} control={form.control} name="type" label="Account type" />
        </div>

        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          hint="Optional"
          description="Additional details about this account"
          className="resize-none"
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="openingBalance"
            label="Opening Balance"
            description="The account initial balance"
            type="number"
            step="0.01"
            placeholder="0.00"
          />

          <InputColor
            control={form.control}
            name="color"
            label="Color"
            hint="Optional"
            description="Choose a color to help identify this account"
          />
        </div>

        <DialogFooter>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? <LoaderIcon className="animate-spin" /> : "Create account"}
          </Button>
          <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
