import type { AccountGroupCreateRequest, AccountGroupUpdateRequest } from "~/api/types.gen";
import type { AccountGroup } from "../types";

import { useEffect } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";

import { zAccountGroupCreateRequest, zAccountGroupUpdateRequest } from "~/api/zod.gen";
import { InputColor, InputText } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: AccountGroupCreateRequest | AccountGroupUpdateRequest) => void;
  accountGroup?: AccountGroup | null;
  isLoading?: boolean;
}

const defaultColor = "#be32cc";

export default function AccountGroupDialog({ open, onOpenChange, onSubmit, accountGroup, isLoading = false }: Props) {
  const isEditing = !!accountGroup;

  const form = useForm<AccountGroupCreateRequest | AccountGroupUpdateRequest>({
    resolver: zodResolver(isEditing ? zAccountGroupUpdateRequest : zAccountGroupCreateRequest),
    defaultValues: {
      name: "",
      color: defaultColor,
      iconUrl: "",
    },
  });

  // Reset form when dialog opens/closes or accountGroup changes
  useEffect(() => {
    if (open) {
      if (accountGroup) {
        form.reset({
          name: accountGroup.name,
          color: accountGroup.color ?? "",
          iconUrl: accountGroup.iconUrl ?? "",
        });
      } else {
        form.reset({
          name: "",
          color: defaultColor,
          iconUrl: "",
        });
      }
    }
  }, [open, accountGroup, form]);

  const handleSubmit = form.handleSubmit((data) => {
    // Convert empty strings to null
    const processedData = {
      ...data,
      color: data.color?.trim() || undefined,
      iconUrl: data.iconUrl?.trim() || undefined,
    };

    onSubmit(processedData);
  });

  const handleClose = () => {
    onOpenChange(false);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Account Group" : "Create Account Group"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update the account group details below."
              : "Create a new account group to organize your accounts."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={handleSubmit} className="space-y-4">
            <InputText
              control={form.control}
              name="name"
              label="Group Name"
              placeholder="e.g., Bank Accounts, Credit Cards"
            />

            <InputColor
              control={form.control}
              name="color"
              label="Color"
              hint="Optional"
              description="Choose a color to help identify this group"
            />

            <InputText
              control={form.control}
              type="url"
              name="iconUrl"
              label="Icon URL"
              hint="Optional"
              placeholder="https://example.com/icon.png"
              description="URL to an icon image for this group"
            />

            <DialogFooter>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : isEditing ? "Update Group" : "Create Group"}
              </Button>
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                Cancel
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
