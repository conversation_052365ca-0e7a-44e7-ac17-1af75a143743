import type { Account } from "../types";

import { Link } from "@tanstack/react-router";
import { format as formatDate } from "date-fns";
import { ArrowLeft, Edit, ExternalLink, Trash2 } from "lucide-react";

import { LoadingIndicator } from "~/components/elements";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { getAccountTypeIcon, getAccountTypeLabel } from "../utils";

interface Props {
  account: Account;
  onEdit: (account: Account) => void;
  onDelete: (account: Account) => void;
  isLoading?: boolean;
}

export default function AccountDetails({ account, onEdit, onDelete, isLoading = false }: Props) {
  const baseCurrency = useBaseCurrency();

  const Icon = getAccountTypeIcon(account.type);
  const typeLabel = getAccountTypeLabel(account.type);

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="mt-8 space-y-6">
      {/* Back navigation */}
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="sm" asChild>
          <Link to="/accounts">
            <ArrowLeft className="mr-1" />
            Back to Accounts
          </Link>
        </Button>
      </div>

      {/* Account header */}
      <div className="flex flex-col justify-between gap-4 sm:flex-row">
        <div className="flex items-start gap-4">
          <div
            className="flex-shrink-0 rounded-lg border p-3"
            style={{ backgroundColor: account.color ? `${account.color}20` : undefined }}
          >
            <Icon className="h-6 w-6" style={{ color: account.color || undefined }} />
          </div>

          <div>
            <h1 className="text-foreground text-2xl font-bold">{account.name}</h1>
            <p className="text-muted-foreground">
              {typeLabel} • {account.currency}
              {account.group && (
                <>
                  {" • "}
                  <span className="font-medium">{account.group.name}</span>
                </>
              )}
            </p>

            <div className="mt-2 flex items-center gap-2">
              {!account.isActive && <Badge variant="secondary">Inactive</Badge>}
              {account.color && (
                <div
                  className="size-4 rounded-full border"
                  style={{ backgroundColor: account.color }}
                  title={`Color: ${account.color}`}
                />
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2 sm:flex-row">
          <div>
            <Button variant="secondary" onClick={() => onEdit(account)}>
              <Edit className="mr-1" />
              Edit Account
            </Button>
          </div>
          <div>
            <Button variant="secondary" onClick={() => onDelete(account)}>
              <Trash2 className="mr-1" />
              Delete Account
            </Button>
          </div>
        </div>
      </div>

      {/* Balance cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Current Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-foreground text-2xl font-bold">
              {formatCurrency(account.currency, account.currentBalance)}
            </div>
            {account.currency !== baseCurrency && (
              <div className="text-muted-foreground text-lg">
                {formatCurrency(baseCurrency, account.baseCurrentBalance)}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Opening Balance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-foreground text-2xl font-bold">
              {formatCurrency(account.currency, account.openingBalance)}
            </div>
            {account.currency !== baseCurrency && (
              <div className="text-muted-foreground text-lg">
                {formatCurrency(baseCurrency, account.baseOpeningBalance)}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Account information */}
      <Card>
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
            <div>
              <h4 className="text-foreground mb-1 font-medium">Account Type</h4>
              <p className="text-muted-foreground">{typeLabel}</p>
            </div>

            <div>
              <h4 className="text-foreground mb-1 font-medium">Currency</h4>
              <p className="text-muted-foreground">{account.currency}</p>
            </div>

            <div>
              <h4 className="text-foreground mb-1 font-medium">Status</h4>
              <p className="text-muted-foreground">{account.isActive ? "Active" : "Inactive"}</p>
            </div>

            {account.group && (
              <div>
                <h4 className="text-foreground mb-1 font-medium">Group</h4>
                <p className="text-muted-foreground">{account.group.name}</p>
              </div>
            )}

            {account.overdraftLimit && parseFloat(account.overdraftLimit) > 0 && (
              <div>
                <h4 className="text-foreground mb-1 font-medium">Overdraft Limit</h4>
                <p className="text-muted-foreground">{formatCurrency(account.currency, account.overdraftLimit)}</p>
              </div>
            )}

            <div>
              <h4 className="text-foreground mb-1 font-medium">Created</h4>
              <p className="text-muted-foreground">{formatDate(account.createdAt, "PPP")}</p>
            </div>
          </div>

          {account.description && (
            <div>
              <h4 className="text-foreground mb-1 font-medium">Description</h4>
              <p className="text-muted-foreground">{account.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" asChild>
              <Link to="/transactions" search={{ accountId: account.id }}>
                <ExternalLink className="mr-1" />
                View Transactions
              </Link>
            </Button>

            <Button variant="outline" disabled>
              <ExternalLink className="mr-1" />
              Update Balance
              <span className="text-muted-foreground ml-2 text-xs">(Coming Soon)</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* TODO: Add recent transactions section */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground py-8 text-center">
            <p>Transaction integration will be implemented in a future update</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
