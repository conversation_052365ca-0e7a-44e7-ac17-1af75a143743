import type { Account, AccountGroup } from "~/api/types.gen";

import { useMemo } from "react";

import { groupAccountsByGroup } from "../utils";
import AccountCard from "./account-card";
import AccountGroupSection from "./account-group-section";

interface Props {
  accounts: Account[];
  accountGroups: AccountGroup[];
  onEditAccount: (account: Account) => void;
  onDeleteAccount: (account: Account) => void;
  onEditAccountGroup: (group: AccountGroup) => void;
  onDeleteAccountGroup: (group: AccountGroup) => void;
  onAddAccountToGroup: (group: AccountGroup) => void;
}

export default function AccountsListGrouped(props: Props) {
  const {
    accounts,
    accountGroups,
    onEditAccount,
    onDeleteAccount,
    onEditAccountGroup,
    onDeleteAccountGroup,
    onAddAccountToGroup,
  } = props;

  const groupedData = useMemo(() => groupAccountsByGroup(accounts, accountGroups), [accounts, accountGroups]);

  return (
    <div className="space-y-8">
      {/* Grouped accounts */}
      {groupedData.grouped.map(({ group, accounts }) => (
        <AccountGroupSection
          key={group.id}
          group={group}
          accounts={accounts}
          onEditGroup={onEditAccountGroup}
          onDeleteGroup={onDeleteAccountGroup}
          onEditAccount={onEditAccount}
          onDeleteAccount={onDeleteAccount}
          onAddAccountToGroup={onAddAccountToGroup}
        />
      ))}

      {/* Ungrouped accounts */}
      {groupedData.ungrouped.length > 0 && (
        <div className="space-y-3">
          <h2 className="text-foreground font-semibold">Ungrouped Accounts</h2>
          {groupedData.ungrouped.map((account) => (
            <AccountCard key={account.id} account={account} onEdit={onEditAccount} onDelete={onDeleteAccount} />
          ))}
        </div>
      )}
    </div>
  );
}
