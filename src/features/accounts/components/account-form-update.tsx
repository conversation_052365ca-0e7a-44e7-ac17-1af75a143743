import type { AccountUpdateRequest } from "~/api/types.gen";
import type { Account, AccountGroup } from "../types";

import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderIcon } from "lucide-react";
import z from "zod";

import { zAccountUpdateRequest } from "~/api/zod.gen";
import { InputColor, InputCurrency, InputSelect, InputSwitch, InputText, InputTextarea } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import { DialogFooter } from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";

import { accountTypeOptions } from "../constants";

interface Props {
  account: Account;
  accountGroups: AccountGroup[];
  onSubmit: (data: AccountUpdateRequest) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export default function AccountFormUpdate(props: Props) {
  const { account, accountGroups, onSubmit, onCancel, isLoading = false } = props;

  const defaultValues = useMemo<AccountUpdateRequest>(
    () => ({
      name: account.name,
      type: account.type,
      currency: account.currency,
      groupId: account.groupId,
      description: account.description || "",
      color: account.color || "",
      overdraftLimit: account.overdraftLimit || "0.00",
      isActive: account.isActive,
    }),
    [account]
  );

  const form = useForm<AccountUpdateRequest>({
    resolver: zodResolver(
      zAccountUpdateRequest.merge(
        z.object({
          groupId: z.string().uuid().nullish(),
        })
      )
    ),
    defaultValues,
  });

  const resetForm = form.reset;

  // Reset form when account changes
  useEffect(() => {
    resetForm(defaultValues);
  }, [defaultValues, resetForm]);

  const handleSubmit = form.handleSubmit((data: AccountUpdateRequest) => {
    // Convert empty strings to null/undefined
    const processedData = {
      ...data,
      description: data.description?.trim() || null,
      color: data.color?.trim() || undefined,
      overdraftLimit: data.overdraftLimit?.trim() || undefined,
      groupId: data.groupId || null,
    };

    onSubmit(processedData);
  });

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="name"
            label="Account Name"
            placeholder="e.g., Main Checking Account"
          />

          <InputSelect
            values={accountGroups.map((group) => ({ value: group.id, label: group.name }))}
            control={form.control}
            name="groupId"
            label="Account group"
            hint="Optional"
            placeholder="No group"
            clearable
          />

          <InputCurrency control={form.control} name="currency" label="Currency" />

          <InputSelect values={accountTypeOptions} control={form.control} name="type" label="Account type" />
        </div>

        <InputTextarea
          control={form.control}
          name="description"
          label="Description"
          hint="Optional"
          description="Additional details about this account"
          className="resize-none"
        />

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText
            control={form.control}
            name="overdraftLimit"
            label="Overdraft Limit"
            hint="Optional"
            description="Maximum amount you can go below zero"
            type="number"
            step="0.01"
            placeholder="0.00"
          />

          <InputColor
            control={form.control}
            name="color"
            label="Color"
            hint="Optional"
            description="Choose a color to help identify this account"
          />

          <InputSwitch
            control={form.control}
            name="isActive"
            label="Is active?"
            description="Inactive accounts are hidden from most views"
          />
        </div>

        <DialogFooter>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? <LoaderIcon className="animate-spin" /> : "Save changes"}
          </Button>
          <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
