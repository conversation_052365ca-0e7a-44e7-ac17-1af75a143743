import type { Account, AccountGroup, AccountType } from "~/api/types.gen";

import { useMemo, useState } from "react";

import { Filter, PlusIcon, Search } from "lucide-react";

import { LoadingIndicator } from "~/components/elements";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency } from "~/lib/formatters";

import { getAccountTypeLabel } from "../utils";
import AccountsListFlat from "./accounts-list-flat";
import AccountsListGrouped from "./accounts-list-grouped";

interface Props {
  accounts: Account[];
  accountGroups: AccountGroup[];
  onCreateAccount: () => void;
  onCreateAccountGroup: () => void;
  onEditAccount: (account: Account) => void;
  onDeleteAccount: (account: Account) => void;
  onEditAccountGroup: (group: AccountGroup) => void;
  onDeleteAccountGroup: (group: AccountGroup) => void;
  onAddAccountToGroup: (group: AccountGroup) => void;
  isLoading?: boolean;
}

export default function AccountsList(props: Props) {
  const {
    accounts,
    accountGroups,
    onCreateAccount,
    onCreateAccountGroup,
    onEditAccount,
    onDeleteAccount,
    onEditAccountGroup,
    onDeleteAccountGroup,
    onAddAccountToGroup,
    isLoading = false,
  } = props;

  const baseCurrency = useBaseCurrency();

  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState<AccountType | "all">("all");
  const [viewMode, setViewMode] = useState<"grouped" | "flat">("grouped");

  // Filter accounts based on search and type filter
  const filteredAccounts = useMemo(
    () =>
      accounts.filter((account) => {
        const matchesSearch =
          account.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          account.description?.toLowerCase().includes(searchQuery.toLowerCase());
        const matchesType = typeFilter === "all" || account.type === typeFilter;

        return matchesSearch && matchesType;
      }),
    [accounts, searchQuery, typeFilter]
  );

  const totalBalance = useMemo(
    () =>
      filteredAccounts.reduce((sum, account) => {
        return sum + parseFloat(account.baseCurrentBalance || "0");
      }, 0),
    [filteredAccounts]
  );

  if (isLoading) {
    return <LoadingIndicator />;
  }

  return (
    <div className="space-y-6">
      {/* Header with actions */}
      <div className="flex flex-col justify-between gap-4 sm:flex-row">
        <div className="flex flex-1 flex-col gap-4 sm:flex-row">
          <div className="relative max-w-sm flex-1">
            <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
            <Input
              placeholder="Search accounts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as AccountType | "all")}>
            <SelectTrigger className="w-full sm:w-48">
              <Filter className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="cash">{getAccountTypeLabel("cash")}</SelectItem>
              <SelectItem value="card">{getAccountTypeLabel("card")}</SelectItem>
              <SelectItem value="bank_account">{getAccountTypeLabel("bank_account")}</SelectItem>
              <SelectItem value="savings">{getAccountTypeLabel("savings")}</SelectItem>
              <SelectItem value="loan">{getAccountTypeLabel("loan")}</SelectItem>
              <SelectItem value="other">{getAccountTypeLabel("other")}</SelectItem>
            </SelectContent>
          </Select>

          <Select value={viewMode} onValueChange={(value) => setViewMode(value as "grouped" | "flat")}>
            <SelectTrigger className="w-full sm:w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="grouped">Grouped</SelectItem>
              <SelectItem value="flat">Flat</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={onCreateAccountGroup}>
            <PlusIcon className="mr-1" />
            Add Group
          </Button>
          <Button onClick={onCreateAccount}>
            <PlusIcon className="mr-1" />
            Add Account
          </Button>
        </div>
      </div>

      {/* Balance overview */}
      <div className="bg-card rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-foreground font-semibold">Total Balance</h3>
            <p className="text-muted-foreground text-sm">
              {filteredAccounts.length} account{filteredAccounts.length !== 1 ? "s" : ""}
            </p>
          </div>
          <div className="text-right">
            <div className="text-foreground text-2xl font-bold">
              {formatCurrency(baseCurrency, totalBalance.toFixed(4))}
            </div>
          </div>
        </div>
      </div>

      {/* Accounts display */}
      {filteredAccounts.length === 0 ? (
        <div className="py-12 text-center">
          <p className="text-muted-foreground mb-4">
            {searchQuery || typeFilter !== "all" ? "No accounts match your filters" : "No accounts found"}
          </p>
          {!searchQuery && typeFilter === "all" && (
            <Button onClick={onCreateAccount}>
              <PlusIcon className="mr-2 h-4 w-4" />
              Create your first account
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {viewMode === "grouped" && (
            <AccountsListGrouped
              accounts={filteredAccounts}
              accountGroups={accountGroups}
              onEditAccount={onEditAccount}
              onDeleteAccount={onDeleteAccount}
              onEditAccountGroup={onEditAccountGroup}
              onDeleteAccountGroup={onDeleteAccountGroup}
              onAddAccountToGroup={onAddAccountToGroup}
            />
          )}
          {viewMode === "flat" && (
            <AccountsListFlat
              accounts={filteredAccounts}
              onEditAccount={onEditAccount}
              onDeleteAccount={onDeleteAccount}
            />
          )}
        </div>
      )}
    </div>
  );
}
