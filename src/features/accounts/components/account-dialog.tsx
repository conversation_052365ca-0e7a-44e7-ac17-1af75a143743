import type { AccountCreateRequest, AccountUpdateRequest } from "~/api/types.gen";
import type { Account, AccountGroup } from "../types";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "~/components/ui/dialog";

import AccountFormCreate from "./account-form-create";
import AccountFormUpdate from "./account-form-update";

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: AccountCreateRequest | AccountUpdateRequest) => void;
  account?: Account | null;
  accountGroups: AccountGroup[];
  preselectedGroupId?: string | null;
  isLoading?: boolean;
}

export default function AccountDialog(props: Props) {
  const { open, onOpenChange, onSubmit, account, accountGroups, preselectedGroupId, isLoading = false } = props;

  const isEditing = !!account;

  const handleClose = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Account" : "Create Account"}</DialogTitle>
          <DialogDescription>
            {isEditing ? "Update the account details below." : "Create a new financial account to track your money."}
          </DialogDescription>
        </DialogHeader>

        {isEditing ? (
          <AccountFormUpdate
            account={account}
            accountGroups={accountGroups}
            onSubmit={onSubmit}
            onCancel={handleClose}
            isLoading={isLoading}
          />
        ) : (
          <AccountFormCreate
            accountGroups={accountGroups}
            preselectedGroupId={preselectedGroupId}
            onSubmit={onSubmit}
            onCancel={handleClose}
            isLoading={isLoading}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
