import type { AccountCreateRequest, AccountUpdateRequest } from "~/api/types.gen";
import type { Account, AccountGroup, AccountType } from "../types";

import { useEffect } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import z from "zod";

import { zAccountCreateRequest, zAccountUpdateRequest } from "~/api/zod.gen";
import { InputColor, InputCurrency, InputSelect, InputText } from "~/components/inputs";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Switch } from "~/components/ui/switch";
import { Textarea } from "~/components/ui/textarea";
import { useBaseCurrency } from "~/features/auth/hooks";

import { getAccountTypeLabel } from "../utils";

interface Props {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: AccountCreateRequest | AccountUpdateRequest) => void;
  account?: Account | null;
  accountGroups: AccountGroup[];
  preselectedGroupId?: string | null;
  isLoading?: boolean;
}

export default function AccountDialog(props: Props) {
  const { open, onOpenChange, onSubmit, account, accountGroups, preselectedGroupId, isLoading = false } = props;

  const baseCurrency = useBaseCurrency();

  const isEditing = !!account;

  const form = useForm<AccountCreateRequest | AccountUpdateRequest>({
    resolver: zodResolver(
      (isEditing ? zAccountUpdateRequest : zAccountCreateRequest).merge(
        z.object({
          groupId: z.string().uuid().nullish(),
        })
      )
    ),
    defaultValues: {
      name: "",
      type: "cash",
      currency: baseCurrency,
      groupId: null,
      description: "",
      color: "",
      openingBalance: "0.00",
      overdraftLimit: "0.00",
      isActive: true,
    },
  });
  const resetForm = form.reset;

  // Reset form when dialog opens/closes or account changes
  useEffect(() => {
    if (open) {
      if (account) {
        resetForm({
          name: account.name,
          type: account.type,
          currency: account.currency,
          groupId: account.groupId,
          description: account.description || "",
          color: account.color || "",
          openingBalance: account.openingBalance || "0.00",
          overdraftLimit: account.overdraftLimit || "0.00",
          isActive: account.isActive,
        });
      } else {
        resetForm({
          name: "",
          type: "cash",
          currency: baseCurrency,
          groupId: preselectedGroupId ?? "",
          description: "",
          color: "",
          openingBalance: "0.00",
          overdraftLimit: "0.00",
          isActive: true,
        });
      }
    }
  }, [open, account, preselectedGroupId, baseCurrency, resetForm]);

  const handleSubmit = form.handleSubmit((data: AccountCreateRequest | AccountUpdateRequest) => {
    // Convert empty strings to null
    const processedData = {
      ...data,
      description: data.description?.trim() || null,
      color: data.color?.trim() || undefined,
      overdraftLimit: data.overdraftLimit?.trim() || undefined,
      groupId: data.groupId || null,
    };

    onSubmit(processedData);
  });

  const handleClose = () => {
    onOpenChange(false);
    resetForm();
  };

  const accountTypes: AccountType[] = ["cash", "card", "bank_account", "savings", "loan", "other"];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Account" : "Create Account"}</DialogTitle>
          <DialogDescription>
            {isEditing ? "Update the account details below." : "Create a new financial account to track your money."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <InputText
                control={form.control}
                name="name"
                label="Account Name"
                placeholder="e.g., Main Checking Account"
              />

              <InputSelect
                values={accountGroups.map((group) => ({ value: group.id, label: group.name }))}
                control={form.control}
                name="groupId"
                label="Account group"
                hint="Optional"
                placeholder="No group"
                clearable
              />

              <InputCurrency control={form.control} name="currency" label="Currency" />

              <InputSelect
                values={accountTypes.map((type) => ({ value: type, label: getAccountTypeLabel(type) }))}
                control={form.control}
                name="type"
                label="Account type"
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional details about this account..."
                      className="resize-none"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {!isEditing && (
                <InputText
                  control={form.control}
                  name="openingBalance"
                  label="Opening Balance"
                  description="The account initial balance"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                />
              )}

              <InputText
                control={form.control}
                name="overdraftLimit"
                label="Overdraft Limit"
                hint="Optional"
                description="Maximum amount you can go below zero"
                type="number"
                step="0.01"
                placeholder="0.00"
              />

              <InputColor
                control={form.control}
                name="color"
                label="Color"
                hint="Optional"
                description="Choose a color to help identify this account"
              />

              {isEditing && (
                <>
                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="border-input flex flex-row items-center justify-between rounded-lg border bg-white p-4 md:col-span-2">
                        <div className="space-y-0.5">
                          <FormLabel className="text-sm">Is active?</FormLabel>
                          <FormDescription>Inactive accounts are hidden from most views</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </>
              )}
            </div>

            <DialogFooter>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Saving..." : isEditing ? "Update Account" : "Create Account"}
              </Button>
              <Button type="button" variant="outline" onClick={handleClose} disabled={isLoading}>
                Cancel
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
