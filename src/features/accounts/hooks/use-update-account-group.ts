import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { 
  updateAccountGroupMutation, 
  listAccountGroupsQueryKey,
  getAccountGroupQueryKey,
  listAccountsQueryKey 
} from "~/api/@tanstack/react-query.gen";

import type { AccountGroupFormData } from "../types";

export function useUpdateAccountGroup() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...updateAccountGroupMutation(),
    onSuccess: (data, variables) => {
      // Invalidate account groups query to refresh the list
      queryClient.invalidateQueries({ queryKey: listAccountGroupsQueryKey() });
      
      // Invalidate accounts query since group info might be included
      queryClient.invalidateQueries({ queryKey: listAccountsQueryKey() });
      
      // Invalidate the specific account group query
      queryClient.invalidateQueries({ queryKey: getAccountGroupQuery<PERSON>ey({ path: { id: variables.path.id } }) });
      
      toast.success("Account group updated successfully!", {
        description: `${data.name} has been updated.`,
      });
    },
    onError: (error) => {
      toast.error("Failed to update account group", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const updateAccountGroup = (groupId: string, data: AccountGroupFormData) => {
    mutation.mutate({
      path: { id: groupId },
      body: {
        name: data.name,
        color: data.color || undefined,
        iconUrl: data.iconUrl || undefined,
      },
    });
  };

  return {
    updateAccountGroup,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
