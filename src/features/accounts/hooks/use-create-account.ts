import type { AccountFormData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  createAccountMutation,
  listAccountGroupsQueryKey,
  listAccountsQueryKey,
} from "~/api/@tanstack/react-query.gen";

export function useCreateAccount() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...createAccountMutation(),
    onSuccess: (data) => {
      // Invalidate accounts and account groups queries to refresh the lists
      queryClient.invalidateQueries({ queryKey: listAccountsQueryKey() });
      queryClient.invalidateQueries({ queryKey: listAccountGroupsQueryKey() });

      toast.success("Account created successfully!", {
        description: `${data.name} has been added to your accounts.`,
      });
    },
    onError: (error) => {
      toast.error("Failed to create account", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const createAccount = (data: AccountFormData) => {
    mutation.mutate({
      body: {
        name: data.name,
        type: data.type,
        currency: data.currency as any,
        groupId: data.groupId || null,
        description: data.description || undefined,
        color: data.color || undefined,
        openingBalance: data.openingBalance,
        overdraftLimit: data.overdraftLimit || undefined,
      },
    });
  };

  return {
    createAccount,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
