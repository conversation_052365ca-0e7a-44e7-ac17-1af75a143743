import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  deleteAccountGroupMutation,
  listAccountGroupsQueryKey,
  listAccountsQueryKey,
} from "~/api/@tanstack/react-query.gen";

export function useDeleteAccountGroup() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...deleteAccountGroupMutation(),
    onSuccess: () => {
      // Invalidate account groups query to refresh the list
      queryClient.invalidateQueries({ queryKey: listAccountGroupsQueryKey() });

      // Invalidate accounts query since accounts in this group will be ungrouped
      queryClient.invalidateQueries({ queryKey: listAccountsQueryKey() });

      toast.success("Account group deleted successfully!", {
        description: "The group has been removed and accounts have been ungrouped.",
      });
    },
    onError: (error) => {
      toast.error("Failed to delete account group", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const deleteAccountGroup = (groupId: string) => {
    mutation.mutate({
      path: { id: groupId },
    });
  };

  return {
    deleteAccountGroup,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
