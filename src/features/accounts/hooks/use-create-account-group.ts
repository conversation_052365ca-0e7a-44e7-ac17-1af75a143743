import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { 
  createAccountGroupMutation, 
  listAccountGroupsQueryKey 
} from "~/api/@tanstack/react-query.gen";

import type { AccountGroupFormData } from "../types";

export function useCreateAccountGroup() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...createAccountGroupMutation(),
    onSuccess: (data) => {
      // Invalidate account groups query to refresh the list
      queryClient.invalidateQueries({ queryKey: listAccountGroupsQueryKey() });
      
      toast.success("Account group created successfully!", {
        description: `${data.name} has been added to your account groups.`,
      });
    },
    onError: (error) => {
      toast.error("Failed to create account group", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const createAccountGroup = (data: AccountGroupFormData) => {
    mutation.mutate({
      body: {
        name: data.name,
        color: data.color || undefined,
        iconUrl: data.iconUrl || undefined,
      },
    });
  };

  return {
    createAccountGroup,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
