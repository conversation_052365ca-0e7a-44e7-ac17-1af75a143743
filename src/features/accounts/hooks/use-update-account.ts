import type { AccountFormData } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  getAccountQueryKey,
  listAccountGroupsQuery<PERSON>ey,
  listAccountsQuery<PERSON>ey,
  updateAccountMutation,
} from "~/api/@tanstack/react-query.gen";

export function useUpdateAccount() {
  const queryClient = useQueryClient();

  const mutation = useMutation({
    ...updateAccountMutation(),
    onSuccess: (data, variables) => {
      // Invalidate accounts and account groups queries to refresh the lists
      queryClient.invalidateQueries({ queryKey: listAccountsQueryKey() });
      queryClient.invalidateQueries({ queryKey: listAccountGroupsQueryKey() });

      // Invalidate the specific account query
      queryClient.invalidateQueries({ queryKey: getAccountQueryKey({ path: { id: variables.path.id } }) });

      toast.success("Account updated successfully!", {
        description: `${data.name} has been updated.`,
      });
    },
    onError: (error) => {
      toast.error("Failed to update account", {
        description: error.message || "An unexpected error occurred.",
      });
    },
  });

  const updateAccount = (accountId: string, data: AccountFormData) => {
    mutation.mutate({
      path: { id: accountId },
      body: {
        name: data.name,
        type: data.type,
        currency: data.currency as any,
        groupId: data.groupId || null,
        description: data.description || undefined,
        color: data.color || undefined,
        overdraftLimit: data.overdraftLimit || undefined,
        isActive: data.isActive,
      },
    });
  };

  return {
    updateAccount,
    isLoading: mutation.isPending,
    error: mutation.error,
  };
}
