// This file is auto-generated by @hey-api/openapi-ts

import { z } from "zod";

export const zTokenResponse = z.object({
  accessToken: z.string(),
});

export const zCommonError = z.object({
  success: z.boolean().optional().default(false),
  message: z.string(),
  stack: z.string().optional(),
});

export const zValidationError = z.object({
  success: z.boolean().optional().default(false),
  errors: z.array(z.string()),
  message: z.string(),
});

export const zTokenRequest = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

/**
 * ISO 4217 currency code
 */
export const zCurrency = z.enum(["EUR", "PLN", "UAH", "USD"]);

export const zUser = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.union([z.string(), z.null()]),
  baseCurrency: zCurrency,
  isActive: z.boolean().optional(),
});

export const zUserCreateRequest = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  name: z.union([z.string(), z.null()]),
  baseCurrency: zCurrency,
});

export const zAccountGroup = z.object({
  id: z.string().uuid(),
  name: z.string(),
  color: z.union([z.string(), z.null()]),
  iconUrl: z.union([z.string(), z.null()]),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zAccountGroupCreateRequest = z.object({
  name: z.string().min(1).max(100),
  color: z.string().optional(),
  iconUrl: z.string().url().optional(),
});

export const zAccountGroupUpdateRequest = z.object({
  name: z.string().min(1).max(100).optional(),
  color: z.string().optional(),
  iconUrl: z.string().url().optional(),
});

/**
 * Account type
 */
export const zAccountType = z.enum(["cash", "card", "bank_account", "savings", "loan", "other"]);

/**
 * Decimal number with up to 4 decimal places
 */
export const zDecimal = z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/);

export const zAccount = z.object({
  id: z.string().uuid(),
  groupId: z.union([z.string().uuid(), z.null()]),
  group: zAccountGroup.optional(),
  name: z.string(),
  currency: zCurrency,
  type: zAccountType,
  description: z.union([z.string(), z.null()]),
  color: z.union([z.string(), z.null()]),
  overdraftLimit: zDecimal,
  isActive: z.boolean(),
  openingBalance: zDecimal,
  currentBalance: zDecimal,
  baseOpeningBalance: zDecimal,
  baseCurrentBalance: zDecimal,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zAccountCreateRequest = z.object({
  groupId: z.union([z.string().uuid(), z.null()]),
  name: z.string().min(3).max(100),
  currency: zCurrency,
  type: zAccountType,
  description: z.union([z.string(), z.null()]).optional(),
  color: z.union([z.string(), z.null()]).optional(),
  overdraftLimit: zDecimal.and(z.union([z.string(), z.null()])).optional(),
  openingBalance: zDecimal,
});

export const zAccountUpdateRequest = z.object({
  groupId: z.union([z.string().uuid(), z.null()]).optional(),
  name: z.string().min(3).max(100).optional(),
  currency: zCurrency.optional(),
  type: zAccountType.optional(),
  description: z.union([z.string(), z.null()]).optional(),
  color: z.union([z.string(), z.null()]).optional(),
  overdraftLimit: zDecimal.and(z.union([z.string(), z.null()])).optional(),
  isActive: z.boolean().optional(),
});

export const zAccountBalanceUpdateRequest = z.object({
  currentBalance: zDecimal,
});

/**
 * Budget period
 */
export const zBudgetPeriod = z.enum(["week", "month", "quarter", "year"]);

/**
 * Budget type
 */
export const zBudgetType = z.enum(["fixed", "percentage"]);

/**
 * Budget record for a specific or current period
 */
export const zBudgetRecord = z.object({
  id: z.string().uuid(),
  budgetId: z.string().uuid(),
  startDate: z.string().date(),
  endDate: z.string().date(),
  plannedAmount: zDecimal,
  usedAmount: zDecimal,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zBudget = z.object({
  id: z.string().uuid(),
  name: z.string(),
  description: z.union([z.string(), z.null()]),
  period: zBudgetPeriod,
  type: zBudgetType,
  value: zDecimal,
  accounts: z.union([z.array(z.string().uuid()), z.null()]),
  isArchived: z.boolean(),
  currentRecord: zBudgetRecord.optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zBudgetListResponse = z.array(zBudget);

export const zBudgetCreateRequest = z.object({
  name: z.string().min(1).max(100),
  description: z.union([z.string(), z.null()]).optional(),
  period: zBudgetPeriod,
  type: zBudgetType,
  value: zDecimal,
  accounts: z.union([z.array(z.string().uuid()), z.null()]).optional(),
});

export const zBudgetUpdateRequest = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.union([z.string(), z.null()]).optional(),
  type: zBudgetType.optional(),
  value: zDecimal.optional(),
  accounts: z.union([z.array(z.string().uuid()), z.null()]).optional(),
  isArchived: z.boolean().optional(),
});

export const zPaginationMeta = z.object({
  total: z.number().int(),
  page: z.number().int(),
  limit: z.number().int(),
  count: z.number().int(),
});

export const zBudgetHistoryResponse = z.object({
  items: z.array(zBudgetRecord),
  meta: zPaginationMeta,
});

export const zCategory = z.object({
  id: z.string().uuid(),
  name: z.string(),
  color: z.union([z.string(), z.null()]),
  icon: z.union([z.string(), z.null()]),
  isExpense: z.boolean(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zCategoryCreateRequest = z.object({
  name: z.string().min(1).max(100),
  color: z
    .union([z.string().default("#ff6b6b"), z.null()])
    .optional()
    .default("#ff6b6b"),
  icon: z.union([z.string(), z.null()]).optional(),
  isExpense: z.boolean().optional().default(true),
});

export const zCategoryUpdateRequest = z.object({
  name: z.string().min(1).max(100).optional(),
  color: z.string().optional(),
  icon: z.string().optional(),
  isExpense: z.boolean().optional(),
});

export const zGoal = z.object({
  id: z.string().uuid(),
  name: z.string(),
  accounts: z.array(z.string().uuid()),
  targetAmount: zDecimal.and(z.union([z.string(), z.null()])),
  currentAmount: zDecimal.and(z.unknown()),
  description: z.union([z.string(), z.null()]),
  color: z.union([z.string(), z.null()]),
  iconUrl: z.union([z.string(), z.null()]),
  targetDate: z.union([z.string().date(), z.null()]),
  currency: zCurrency,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zGoalCreateRequest = z.object({
  name: z.string().min(1).max(100),
  accounts: z.array(z.string().uuid()).min(1),
  targetAmount: zDecimal.optional(),
  description: z.string().optional(),
  color: z.string().optional(),
  iconUrl: z.string().url().optional(),
  targetDate: z.string().date().optional(),
});

export const zGoalUpdateRequest = z.object({
  name: z.string().min(1).max(100).optional(),
  accounts: z.array(z.string().uuid()).min(1).optional(),
  targetAmount: zDecimal.optional(),
  description: z.string().optional(),
  color: z.string().optional(),
  iconUrl: z.string().url().optional(),
  targetDate: z.string().date().optional(),
});

/**
 * Task recurrence period
 */
export const zTaskPeriod = z.enum(["weekly", "monthly", "quarterly", "yearly"]);

/**
 * Task record status
 */
export const zTaskStatus = z.enum(["active", "completed", "skipped"]);

/**
 * Current task record for this period
 */
export const zTaskRecordResponse = z.union([
  z.object({
    id: z.string().uuid(),
    taskId: z.string().uuid(),
    startDate: z.string().date(),
    endDate: z.string().date(),
    status: zTaskStatus,
    amount: zDecimal.and(z.union([z.string(), z.null()])),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
  }),
  z.null(),
]);

export const zTaskResponse = z.object({
  id: z.string().uuid(),
  title: z.string(),
  period: zTaskPeriod,
  description: z.union([z.string(), z.null()]),
  amount: zDecimal.and(z.union([z.string(), z.null()])),
  dueDay: z.union([z.number().int(), z.null()]),
  isArchived: z.boolean(),
  sendNotifications: z.boolean(),
  current: zTaskRecordResponse,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zTaskListResponse = z.object({
  items: z.array(zTaskResponse),
  meta: zPaginationMeta,
});

export const zTaskCreateRequest = z.object({
  title: z.string().min(1).max(100),
  period: zTaskPeriod,
  description: z.union([z.string(), z.null()]).optional(),
  amount: zDecimal.and(z.union([z.string(), z.null()])).optional(),
  dueDay: z.union([z.number().int().gte(1).lte(31), z.null()]).optional(),
  sendNotifications: z.boolean().optional().default(false),
});

export const zTaskUpdateRequest = z.object({
  title: z.string().min(1).max(100).optional(),
  period: zTaskPeriod.optional(),
  description: z.string().optional(),
  amount: zDecimal.and(z.unknown()).optional(),
  dueDay: z.number().int().gte(1).lte(31).optional(),
  isArchived: z.boolean().optional(),
  sendNotifications: z.boolean().optional(),
});

export const zTaskStatusUpdateRequest = z.object({
  status: z.enum(["completed", "skipped"]),
  amount: zDecimal.and(z.unknown()).optional(),
});

export const zTaskHistoryResponse = z.object({
  items: z.array(z.union([zTaskRecordResponse, z.object({})])),
  meta: zPaginationMeta,
});

export const zTaskTransactionAssignment = z.object({
  transactions: z.array(z.string().uuid()),
});

/**
 * Transaction type
 */
export const zTransactionType = z.enum(["income", "expense", "transfer"]);

export const zTransactionResponse = z.object({
  id: z.string().uuid(),
  transactionDate: z.string().date(),
  categoryId: z.union([z.string().uuid(), z.null()]),
  category: zCategory.optional(),
  type: zTransactionType,
  description: z.union([z.string(), z.null()]),
  accountId: z.string().uuid(),
  account: zAccount,
  amount: zDecimal,
  accountToId: z.union([z.string().uuid(), z.null()]),
  accountTo: zAccount.optional(),
  amountTo: zDecimal.and(z.union([z.string(), z.null()])),
  baseAmount: zDecimal,
  baseAmountTo: zDecimal.and(z.union([z.string(), z.null()])),
  taskRecordId: z.union([z.string().uuid(), z.null()]),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const zTransactionListMeta = z.object({
  total: z.number().int().gte(0),
  page: z.number().int().gte(1),
  limit: z.number().int().gte(1),
  count: z.number().int().gte(0),
});

export const zTransactionListResponse = z.object({
  items: z.array(zTransactionResponse),
  meta: zTransactionListMeta,
});

export const zTransactionCreateRequest = z.object({
  transactionDate: z.string().date(),
  categoryId: z.union([z.string().uuid(), z.null()]).optional(),
  type: zTransactionType,
  description: z.union([z.string(), z.null()]).optional(),
  accountId: z.string().uuid(),
  amount: zDecimal,
  accountToId: z.union([z.string().uuid(), z.null()]).optional(),
  amountTo: zDecimal.and(z.union([z.string(), z.null()])).optional(),
});

export const zTransactionUpdateRequest = z.object({
  transactionDate: z.string().date().optional(),
  categoryId: z.union([z.string().uuid(), z.null()]).optional(),
  type: zTransactionType.optional(),
  description: z.union([z.string(), z.null()]).optional(),
  accountId: z.string().uuid().optional(),
  amount: zDecimal.optional(),
  accountToId: z.union([z.string().uuid(), z.null()]).optional(),
  amountTo: zDecimal.and(z.union([z.string(), z.null()])).optional(),
});

/**
 * Token request
 */
export const zGetTokenData = zTokenRequest;

/**
 * Access token
 */
export const zGetTokenResponse = zTokenResponse;

/**
 * Successfully logged out
 */
export const zLogoutResponse = z.void();

/**
 * User to create
 */
export const zCreateUserData = zUserCreateRequest;

/**
 * User created
 */
export const zCreateUserResponse = zUser;

/**
 * Current user
 */
export const zGetCurrentUserResponse = zUser;

/**
 * Account groups retrieved
 */
export const zListAccountGroupsResponse = z.array(zAccountGroup);

/**
 * Account group to create
 */
export const zCreateAccountGroupData = zAccountGroupCreateRequest;

/**
 * Account group created
 */
export const zCreateAccountGroupResponse = zAccountGroup;

export const zDeleteAccountGroupParameterId = z.string().uuid();

/**
 * Account group deleted
 */
export const zDeleteAccountGroupResponse = z.void();

export const zGetAccountGroupParameterId = z.string().uuid();

/**
 * Account group retrieved
 */
export const zGetAccountGroupResponse = zAccountGroup;

/**
 * Account group data to update
 */
export const zUpdateAccountGroupData = zAccountGroupUpdateRequest;

export const zUpdateAccountGroupParameterId = z.string().uuid();

/**
 * Account group updated
 */
export const zUpdateAccountGroupResponse = zAccountGroup;

/**
 * Accounts retrieved
 */
export const zListAccountsResponse = z.array(zAccount);

/**
 * Account to create
 */
export const zCreateAccountData = zAccountCreateRequest;

/**
 * Account created
 */
export const zCreateAccountResponse = zAccount;

export const zDeleteAccountParameterId = z.string().uuid();

/**
 * Account deleted
 */
export const zDeleteAccountResponse = z.void();

export const zGetAccountParameterId = z.string().uuid();

/**
 * Account retrieved
 */
export const zGetAccountResponse = zAccount;

/**
 * Account data to update
 */
export const zUpdateAccountData = zAccountUpdateRequest;

export const zUpdateAccountParameterId = z.string().uuid();

/**
 * Account updated
 */
export const zUpdateAccountResponse = zAccount;

/**
 * New balance data
 */
export const zUpdateAccountBalanceData = zAccountBalanceUpdateRequest;

export const zUpdateAccountBalanceParameterId = z.string().uuid();

/**
 * Account balance updated
 */
export const zUpdateAccountBalanceResponse = zAccount;

export const zListBudgetsParameterIncludeArchived = z.union([z.boolean().default(false), z.null()]).default(false);

/**
 * Budgets retrieved
 */
export const zListBudgetsResponse = zBudgetListResponse;

/**
 * Budget data
 */
export const zCreateBudgetData = zBudgetCreateRequest;

/**
 * Budget created
 */
export const zCreateBudgetResponse = zBudget;

export const zDeleteBudgetParameterId = z.string().uuid();

/**
 * Budget deleted
 */
export const zDeleteBudgetResponse = z.void();

export const zGetBudgetParameterId = z.string().uuid();

/**
 * Budget retrieved
 */
export const zGetBudgetResponse = zBudget;

/**
 * Budget update data
 */
export const zUpdateBudgetData = zBudgetUpdateRequest;

export const zUpdateBudgetParameterId = z.string().uuid();

/**
 * Budget updated
 */
export const zUpdateBudgetResponse = zBudget;

export const zGetBudgetHistoryParameterId = z.string().uuid();

/**
 * Page number
 */
export const zGetBudgetHistoryParameterPage = z.number().int().gte(1).default(1);

/**
 * Items per page
 */
export const zGetBudgetHistoryParameterLimit = z.number().int().gte(1).lte(100).default(20);

/**
 * Budget history retrieved
 */
export const zGetBudgetHistoryResponse = zBudgetHistoryResponse;

/**
 * Categories retrieved
 */
export const zListCategoriesResponse = z.array(zCategory);

/**
 * Category to create
 */
export const zCreateCategoryData = zCategoryCreateRequest;

/**
 * Category created
 */
export const zCreateCategoryResponse = zCategory;

export const zDeleteCategoryParameterId = z.string().uuid();

/**
 * Category deleted
 */
export const zDeleteCategoryResponse = z.void();

export const zGetCategoryParameterId = z.string().uuid();

/**
 * Category retrieved
 */
export const zGetCategoryResponse = zCategory;

/**
 * Category data to update
 */
export const zUpdateCategoryData = zCategoryUpdateRequest;

export const zUpdateCategoryParameterId = z.string().uuid();

/**
 * Category updated
 */
export const zUpdateCategoryResponse = zCategory;

/**
 * Goals retrieved
 */
export const zListGoalsResponse = z.array(zGoal);

/**
 * Goal to create
 */
export const zCreateGoalData = zGoalCreateRequest;

/**
 * Goal created
 */
export const zCreateGoalResponse = zGoal;

export const zDeleteGoalParameterId = z.string().uuid();

/**
 * Goal deleted
 */
export const zDeleteGoalResponse = z.object({
  message: z.string(),
});

export const zGetGoalParameterId = z.string().uuid();

/**
 * Goal retrieved
 */
export const zGetGoalResponse = zGoal;

/**
 * Goal data to update
 */
export const zUpdateGoalData = zGoalUpdateRequest;

export const zUpdateGoalParameterId = z.string().uuid();

/**
 * Goal updated
 */
export const zUpdateGoalResponse = zGoal;

/**
 * Page number
 */
export const zListTasksParameterPage = z.number().int().gte(1).default(1);

/**
 * Items per page
 */
export const zListTasksParameterLimit = z.number().int().gte(1).lte(100).default(20);

/**
 * Tasks retrieved
 */
export const zListTasksResponse = zTaskListResponse;

/**
 * Task to create
 */
export const zCreateTaskData = zTaskCreateRequest;

/**
 * Task created
 */
export const zCreateTaskResponse = zTaskResponse;

export const zDeleteTaskParameterId = z.string().uuid();

/**
 * Task deleted
 */
export const zDeleteTaskResponse = z.void();

export const zGetTaskParameterId = z.string().uuid();

/**
 * Task retrieved
 */
export const zGetTaskResponse = zTaskResponse;

/**
 * Task data to update
 */
export const zUpdateTaskData = zTaskUpdateRequest;

export const zUpdateTaskParameterId = z.string().uuid();

/**
 * Task updated
 */
export const zUpdateTaskResponse = zTaskResponse;

/**
 * Task status update data
 */
export const zUpdateTaskStatusData = zTaskStatusUpdateRequest;

export const zUpdateTaskStatusParameterId = z.string().uuid();

export const zGetTaskHistoryParameterId = z.string().uuid();

/**
 * Page number
 */
export const zGetTaskHistoryParameterPage = z.number().int().gte(1).default(1);

/**
 * Items per page
 */
export const zGetTaskHistoryParameterLimit = z.number().int().gte(1).lte(100).default(20);

/**
 * Task history retrieved
 */
export const zGetTaskHistoryResponse = zTaskHistoryResponse;

/**
 * Transactions to remove
 */
export const zRemoveTaskTransactionsData = zTaskTransactionAssignment;

export const zRemoveTaskTransactionsParameterId = z.string().uuid();

/**
 * Transactions to assign
 */
export const zAssignTaskTransactionsData = zTaskTransactionAssignment;

export const zAssignTaskTransactionsParameterId = z.string().uuid();

/**
 * Transactions to replace with
 */
export const zReplaceTaskTransactionsData = zTaskTransactionAssignment;

export const zReplaceTaskTransactionsParameterId = z.string().uuid();

export const zListTransactionsParameterPage = z.number().int().gte(1).default(1);

export const zListTransactionsParameterLimit = z.number().int().gte(1).lte(100).default(20);

/**
 * Paginated list of transactions
 */
export const zListTransactionsResponse = zTransactionListResponse;

/**
 * Transaction data
 */
export const zCreateTransactionData = zTransactionCreateRequest;

/**
 * Transaction created successfully
 */
export const zCreateTransactionResponse = zTransactionResponse;

export const zDeleteTransactionParameterId = z.string().uuid();

/**
 * Transaction deleted successfully
 */
export const zDeleteTransactionResponse = z.void();

export const zGetTransactionParameterId = z.string().uuid();

/**
 * Transaction details
 */
export const zGetTransactionResponse = zTransactionResponse;

/**
 * Updated transaction data
 */
export const zUpdateTransactionData = zTransactionUpdateRequest;

export const zUpdateTransactionParameterId = z.string().uuid();

/**
 * Transaction updated successfully
 */
export const zUpdateTransactionResponse = zTransactionResponse;
