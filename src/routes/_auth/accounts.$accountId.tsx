import type { AccountUpdateRequest } from "~/api/types.gen";
import type { Account } from "~/features/accounts";

import { useState } from "react";

import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";

import { getAccountOptions, listAccountGroupsOptions } from "~/api/@tanstack/react-query.gen";
import { ErrorMessage, PageHeader } from "~/components/blocks";
import { LoadingIndicator } from "~/components/elements";
import { AccountDetails, AccountDialog, useDeleteAccount, useUpdateAccount } from "~/features/accounts";
import { useConfirm } from "~/features/ui/confirmations/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const { accountId } = Route.useParams();

  const navigate = useNavigate();
  const ask = useConfirm();

  const [editDialogOpen, setEditDialogOpen] = useState(false);

  // Data fetching
  const accountQuery = useQuery(getAccountOptions({ path: { id: accountId } }));
  const accountGroupsQuery = useQuery(listAccountGroupsOptions());

  // Account operations
  const { updateAccount, isLoading: isUpdatingAccount } = useUpdateAccount();
  const { deleteAccount, isLoading: isDeletingAccount } = useDeleteAccount();

  const account = accountQuery.data;
  const accountGroups = accountGroupsQuery.data || [];
  const isLoading = accountQuery.isLoading || accountGroupsQuery.isLoading;
  const error = accountQuery.error || accountGroupsQuery.error;

  const handleEditAccount = () => {
    setEditDialogOpen(true);
  };

  const handleDeleteAccount = async (account: Account) => {
    const ok = await ask({
      title: "Delete account",
      description: `Are you sure you want to delete "${account.name}"? This action cannot be undone.`,
      confirmText: "Yes, delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (ok) {
      deleteAccount(account.id);
      // Navigate back to accounts list after successful deletion
      void navigate({ to: "/accounts" });
    }
  };

  const handleAccountSubmit = (data: AccountUpdateRequest) => {
    if (account) {
      updateAccount(account.id, data);
      setEditDialogOpen(false);
    }
  };

  if (error) {
    return (
      <>
        <PageHeader title="Account Details" />
        <ErrorMessage title="Failed to load account" error={error} />
      </>
    );
  }

  if (isLoading || !account) {
    return (
      <>
        <PageHeader title="Account Details" />
        <LoadingIndicator />
      </>
    );
  }

  return (
    <>
      <AccountDetails
        account={account}
        onEdit={handleEditAccount}
        onDelete={handleDeleteAccount}
        isLoading={isDeletingAccount}
      />

      <AccountDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        onSubmit={handleAccountSubmit}
        account={account}
        accountGroups={accountGroups}
        isLoading={isUpdatingAccount}
      />
    </>
  );
}
