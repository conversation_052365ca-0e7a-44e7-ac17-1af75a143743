import type {
  Account,
  AccountCreateRequest,
  AccountGroup,
  AccountGroupCreateRequest,
  AccountGroupUpdateRequest,
  AccountUpdateRequest,
} from "~/api/types.gen";

import { useState } from "react";

import { ErrorMessage, PageHeader } from "~/components/blocks";
import {
  AccountDialog,
  AccountGroupDialog,
  AccountsList,
  useAccountsGrouped,
  useCreateAccount,
  useCreateAccountGroup,
  useDeleteAccount,
  useDeleteAccountGroup,
  useUpdateAccount,
  useUpdateAccountGroup,
} from "~/features/accounts";
import { useConfirm } from "~/features/ui/confirmations/hooks";

export const Route = createFileRoute({
  component: RouteComponent,
});

function RouteComponent() {
  const ask = useConfirm();

  const [accountDialogOpen, setAccountDialogOpen] = useState(false);
  const [accountGroupDialogOpen, setAccountGroupDialogOpen] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [editingAccountGroup, setEditingAccountGroup] = useState<AccountGroup | null>(null);
  const [preselectedGroupId, setPreselectedGroupId] = useState<string | null>(null);

  // Data fetching
  const { accounts, accountGroups, isLoading, error } = useAccountsGrouped();

  // Account operations
  const { createAccount, isLoading: isCreatingAccount } = useCreateAccount();
  const { updateAccount, isLoading: isUpdatingAccount } = useUpdateAccount();
  const { deleteAccount } = useDeleteAccount();

  // Account group operations
  const { createAccountGroup, isLoading: isCreatingAccountGroup } = useCreateAccountGroup();
  const { updateAccountGroup, isLoading: isUpdatingAccountGroup } = useUpdateAccountGroup();
  const { deleteAccountGroup } = useDeleteAccountGroup();

  // Account handlers
  const handleCreateAccount = () => {
    setEditingAccount(null);
    setPreselectedGroupId(null);
    setAccountDialogOpen(true);
  };

  const handleEditAccount = (account: Account) => {
    setEditingAccount(account);
    setPreselectedGroupId(null);
    setAccountDialogOpen(true);
  };

  const handleDeleteAccount = async (account: Account) => {
    const ok = await ask({
      title: "Delete account",
      description: `Are you sure you want to delete "${account.name}"? This action cannot be undone.`,
      confirmText: "Yes, delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (ok) {
      deleteAccount(account.id);
    }
  };

  const handleAccountSubmit = (data: AccountCreateRequest | AccountUpdateRequest) => {
    if (editingAccount) {
      updateAccount(editingAccount.id, data);
    } else {
      createAccount(data);
    }
    setAccountDialogOpen(false);
    setEditingAccount(null);
  };

  // Account group handlers
  const handleCreateAccountGroup = () => {
    setEditingAccountGroup(null);
    setAccountGroupDialogOpen(true);
  };

  const handleEditAccountGroup = (group: AccountGroup) => {
    setEditingAccountGroup(group);
    setAccountGroupDialogOpen(true);
  };

  const handleDeleteAccountGroup = async (group: AccountGroup) => {
    const ok = await ask({
      title: "Delete account group",
      description: `Are you sure you want to delete "${group.name}"? Accounts in this group will be ungrouped.`,
      confirmText: "Yes, delete",
      cancelText: "Cancel",
      variant: "destructive",
    });

    if (ok) {
      deleteAccountGroup(group.id);
    }
  };

  const handleAddAccountToGroup = (group: AccountGroup) => {
    setEditingAccount(null);
    setPreselectedGroupId(group.id);
    setAccountDialogOpen(true);
  };

  const handleAccountGroupSubmit = (data: AccountGroupCreateRequest | AccountGroupUpdateRequest) => {
    if (editingAccountGroup) {
      updateAccountGroup(editingAccountGroup.id, data);
    } else {
      createAccountGroup(data);
    }
    setAccountGroupDialogOpen(false);
    setEditingAccountGroup(null);
  };

  if (error) {
    return (
      <>
        <PageHeader title="Accounts" />
        <ErrorMessage title="Failed to load accounts" error={error} />
      </>
    );
  }

  return (
    <>
      <PageHeader title="Accounts" />

      <AccountsList
        accounts={accounts}
        accountGroups={accountGroups}
        onCreateAccount={handleCreateAccount}
        onCreateAccountGroup={handleCreateAccountGroup}
        onEditAccount={handleEditAccount}
        onDeleteAccount={handleDeleteAccount}
        onEditAccountGroup={handleEditAccountGroup}
        onDeleteAccountGroup={handleDeleteAccountGroup}
        onAddAccountToGroup={handleAddAccountToGroup}
        isLoading={isLoading}
      />

      <AccountDialog
        open={accountDialogOpen}
        onOpenChange={setAccountDialogOpen}
        onSubmit={handleAccountSubmit}
        account={editingAccount}
        accountGroups={accountGroups}
        preselectedGroupId={preselectedGroupId}
        isLoading={isCreatingAccount || isUpdatingAccount}
      />

      <AccountGroupDialog
        open={accountGroupDialogOpen}
        onOpenChange={setAccountGroupDialogOpen}
        onSubmit={handleAccountGroupSubmit}
        accountGroup={editingAccountGroup}
        isLoading={isCreatingAccountGroup || isUpdatingAccountGroup}
      />
    </>
  );
}
